import { useAuthStore } from '@/store';
import _ from 'lodash';
import React, { useEffect, useState } from 'react'
import { useQueryAgxArea } from '../hooks/useQueryAgxArea';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useNavigate } from 'react-router-dom';
import { useCreateAgxSubArea } from '../hooks/useCreateAgxSubArea';
import { Input } from '@/components/ui/input';

const initialState = {
    agxAreas: [],
    agxSubAreas: [],
    agxSubAreaModal: [],
    loading: true,
    error: false
}

export const SubAreaNewPage = () => {

    const navigate = useNavigate();
    const { user } = useAuthStore();
    const agxMerchantNo = user.agxMerchantNo;
    const [dataAreaSetting, setDataAreaSetting] = useState(initialState);
    const [agxSubArea, setAgxSubArea] = useState({
        agx_sub_areaid: null,
        agxSubAreaName: '',
        agxMerchantCreatedNo: agxMerchantNo,
        agxMerchantNos: []
    })

    const { data: GetAreaSettingResponse, isLoading } = useQueryAgxArea({
        agxMerchantNo: user?.agxMerchantNo || "",
    });

    const { createAgxSubAreaAsync, isLoading: isLoadingCreateSubArea } = useCreateAgxSubArea();

    useEffect(() => {
        getDataAreaSetting();
    }, [GetAreaSettingResponse])

    const getDataAreaSetting = async () => {
        try {
            setDataAreaSetting({
                ...dataAreaSetting,
                agxAreas: GetAreaSettingResponse.agxAreas,
                agxSubAreas: GetAreaSettingResponse.agxSubAreas,
                agxSubAreaModal: GetAreaSettingResponse.agxSubAreaModal,
                loading: false,
                error: false
            });
        } catch (error) {
            setDataAreaSetting({
                ...dataAreaSetting,
                agxAreas: [],
                agxSubAreas: [],
                agxSubAreaModal: [],
                loading: true,
                error: true
            })
        }
    }

    const handleChangeSubArea = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.trim();
        setAgxSubArea({ ...agxSubArea, agxSubAreaName: value })
    }

    const handleCreateOrUpdateSubArea = async () => {
        try {
            await createAgxSubAreaAsync(agxSubArea);
            navigate('/admin-store/config');
        } catch (error) {
            console.log(error);
        }
    }

    const handleSelectMerchant = (
        event: React.ChangeEvent<HTMLInputElement>,
        merchantNo: string,
        subAreaId: string
    ) => {
        const isChecked = event.target.checked;
        const checkbox = document.getElementById(merchantNo) as HTMLInputElement | null;
        if (checkbox) {
            checkbox.checked = isChecked;
        }
        const arrSelected = agxSubArea.agxMerchantNos || [];

        let updatedMerchantNos: { agxMerchantNo: string; isSelected: boolean; isHadArea: boolean }[];

        if (arrSelected.length > 0) {
            const filteredMerchantSelected = arrSelected.filter(
                (item: { agxMerchantNo: string }) => item.agxMerchantNo !== merchantNo
            );
            filteredMerchantSelected.push({
                agxMerchantNo: merchantNo,
                isSelected: isChecked,
                isHadArea: subAreaId === agxSubArea.agx_sub_areaid ? true : false
            })
            updatedMerchantNos = filteredMerchantSelected;
        } else {
            updatedMerchantNos = [
                {
                    agxMerchantNo: merchantNo,
                    isSelected: true,
                    isHadArea: false
                }
            ];
        }
        setAgxSubArea({ ...agxSubArea, agxMerchantNos: updatedMerchantNos });
    }
    
    if (isLoading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="pt-4 sm:pt-6 pb-2 px-2 sm:px-4">
            <div className="max-w-full px-2 sm:px-6 md:px-8 lg:px-10 xl:px-28 mt-2 sm:mt-4 lg:mt-10 xl:mt-20 xl:!pr-[20%]">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <h3 className="mb-2 md:mb-0 text-[#6F6F6E] text-[20px] font-medium">サブエリアの作成</h3>
                </div>
                <form>
                    {/* Sub Area Name Input - Mobile Responsive */}
                    <div className="mb-6 md:ml-10 md:px-2 md:flex md:items-center space-y-2 md:space-y-0 md:space-x-2">
                        <label htmlFor="sub-area-name" className="block md:inline text-[#6F6F6E] text-[20px] mb-2 md:mb-0 md:w-[25%] lg:w-[15%] md:min-w-[30%] lg:min-w-[25%]">
                            サブエリアの名称
                        </label>
                        <Input
                            type="text"
                            id="sub-area-name"
                            className="h-[45px] w-full md:min-w-[430px] rounded-[15px] border-gray-300 px-3 py-2 text-[#6F6F6E] text-[20px]"
                            value={agxSubArea.agxSubAreaName}
                            onChange={handleChangeSubArea}
                        />
                    </div>

                    {/* Merchant Selection */}
                    <div className="mb-2 md:ml-10">
                        <label className="block text-[#6F6F6E] mb-2 text-[20px] px-2">
                            サブエリアに紐づけする店舗の選択
                        </label>
                        
                        {/* Table View - Mobile & Desktop */}
                        <div className="bg-transparent rounded-md">
                            {/* Scrollable Container - Mobile horizontal scroll, Desktop vertical scroll */}
                            <div className="max-h-80 overflow-x-auto md:overflow-x-visible md:overflow-y-visible">
                                <div className="min-w-[600px] md:min-w-0">
                                    {/* Header */}
                                    <div className="bg-transparent px-2">
                                        <div className="grid grid-cols-[48px_120px_180px_180px] md:grid-cols-[48px_1fr_1fr_1fr] gap-0 text-[20px] border-b border-[#6F6F6E]">
                                            <div className="px-2 md:px-4 py-3 text-center font-medium text-[#6F6F6E]"></div>
                                            <div className="px-2 md:px-4 py-3 text-center font-medium text-[#6F6F6E]">
                                                <span className="md:hidden">店番号</span>
                                                <span className="hidden md:inline">加盟店番号</span>
                                            </div>
                                            <div className="px-2 md:px-4 py-3 text-center font-medium text-[#6F6F6E]">店舗名</div>
                                            <div className="px-2 md:px-4 py-3 text-center font-medium text-[#6F6F6E]">
                                                <span className="md:hidden">エリア名</span>
                                                <span className="hidden md:inline">サブエリア名</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Body - Desktop gets vertical scroll */}
                                    <div className="md:max-h-80 md:overflow-y-auto">
                                        <div className="divide-y divide-gray-200">
                                            {dataAreaSetting?.agxSubAreaModal?.map((item, index) => (
                                                <div key={index} className="grid grid-cols-[48px_120px_180px_180px] md:grid-cols-[48px_1fr_1fr_1fr] gap-0 hover:bg-gray-50 transition-colors border-none text-[20px]">
                                                    <div className="px-2 md:pr-3 py-4 text-center flex items-center justify-center">
                                                        <Input
                                                            id={item.agxMerchantNo}
                                                            type="checkbox"
                                                            className="form-checkbox h-5 w-5 rounded border-gray-300 text-[#1D9987] accent-[#1D9987] cursor-pointer"
                                                            checked={!!agxSubArea.agxMerchantNos?.find((m) => m.agxMerchantNo === item.agxMerchantNo && m.isSelected)}
                                                            onChange={(e) => handleSelectMerchant(e, item.agxMerchantNo, item.agxSubAreaid)}
                                                        />
                                                    </div>
                                                    <div className="px-2 md:px-4 py-4 text-center flex items-center justify-center text-[#6F6F6E] truncate md:truncate-none">{item.agxMerchantNo}</div>
                                                    <div className="px-2 md:px-4 py-4 text-center flex items-center justify-center text-[#6F6F6E] truncate md:truncate-none">{item.agxStoreName}</div>
                                                    <div className="px-2 md:px-4 py-4 text-center flex items-center justify-center text-[#6F6F6E] truncate md:truncate-none">{item.agxSubAreaName}</div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                {/* Submit Button - Mobile Responsive */}
                <div className="flex flex-col pt-10 mb-6 pb-4 mt-10 items-center sm:items-end justify-center sm:justify-end sm:pr-16">
                    <Button
                        disabled={isLoadingCreateSubArea}
                        className="w-full sm:w-auto md:w-[35%] lg:w-[28%] xl:w-[22%] min-w-[120px] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-[20px]"
                        onClick={handleCreateOrUpdateSubArea}
                    >
                        登錄
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default SubAreaNewPage;