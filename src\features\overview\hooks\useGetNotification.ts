import { useAuthStore } from "@/features/auth/slices/authStore";
import { AccountTypes } from "@/types/globalType";
import { useQuery } from "@tanstack/react-query";
import { notificationService } from "@/features/overview";
import { useGetNotificationInvoiceDeposit } from "./useGetNotificationInvoiceDeposit";

interface Notification {
  url: string,
  title: string,
  isActive: boolean
}

export const useGetNotification = () => {
  const { user, typeStore } = useAuthStore();
  const { notificationData } = useGetNotificationInvoiceDeposit(user?.agxMerchantNo)
  const { notificationData: newNotificationData } = useGetNotificationInvoiceDeposit(user?.agxNewMerchantNo)

  const getUrlConfig = () => {
    const notification: Notification = {
      url: "",
      title: "",
      isActive: false
    }

    if (user?.statusAccount === AccountTypes.APPLICATION_COMPLETE) {
      notification.url = "/store/config";
      notification.title = "申し込みを受け付けました。現在のステータスは加盟店情報よりご確認ください。";
      notification.isActive = true;
    }

    return notification;
  }

  // Query for get notice 
  const {
    data,
    isLoading,
    error
  } = useQuery({
    queryKey: ['notice'],
    queryFn: () => notificationService.getData(),
    enabled: true,
  });


  return {
    noticeData: data,
    notificationData,
    newNotificationData,
    typeStore,
    getUrlConfig
  }
}