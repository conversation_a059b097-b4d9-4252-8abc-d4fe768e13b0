import { formatNumber } from "@/utils/dateUtils";
import { formatNumberJP } from "@/utils/helper";
import { FileText, File } from "lucide-react";
import { Link } from "react-router-dom";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export interface Column {
  key: string;
  label: string;
  className?: string;
}

export interface SectionProps {
  columns: Column[];
  data: any[];
  renderCell?: (column: Column, item: any, index: number) => React.ReactNode;
  className?: string;
  routePrefix?: string; // '/store-invoice-receipt/paygate' or '/store-invoice-receipt/crepico'
  role?: string;
}

export const Section = ({ 
  columns, 
  data, 
  renderCell, 
  className = "", 
  routePrefix = "",
  role = "store"
}: SectionProps) => {

  // Convert url from admin-invoice-receipt to invoice-receipt
  const convertUrl = (url: string) => {
    if (url.startsWith('/admin-invoice-receipt')) {
      return url.replace('/admin-invoice-receipt', '/invoice-receipt');
    }
    return url;
  }

  const defaultRenderCell = (column: Column, item: any, index: number) => {
    const value = item[column.key];

    if (column.key === 'month') {
      return <div className="w-full flex justify-center items-center"><div className="w-[70px] flex justify-start">{value.split("/")[0] + "/" + value.split("/")[1].padStart(2, "0")}</div></div>;
    }
    
    // Handle invoice column
    if (column.key.includes('invoice') && value) {
      return (
        <Link to={`${routePrefix}${value.startsWith('/') ? convertUrl(value) : `/${value}`}`}>
          {(role == 'store' && item['deadlineDate'] !== '-') ? (
          <File className="h-6 w-6 sm:h-6 sm:w-6 mx-auto text-[#C44546] hover:text-[#C44546]/80 cursor-pointer" />
          ) : (
            <File className="h-6 w-6 sm:h-6 sm:w-6 mx-auto text-gray-500 hover:text-gray-600 cursor-pointer" />
          )}
        </Link>
      );
    }
    
    // Handle receipt column
    if (column.key.includes('receipt') && value) {
      return (
        <Link to={`${routePrefix}${value.startsWith('/') ? convertUrl(value) : `/${value}`}`}>
          <FileText className="h-6 w-6 sm:h-6 sm:w-6 mx-auto text-[#1D9987] hover:text-[#1D9987]/80" />
        </Link>
      );
    }
    
    // Handle payment due date styling
    if (column.key === 'deadlineDate' && value !== '-') {
      return <span className="text-[#C44546]">{value}</span>;
    }

    if (column.key === 'total') {
      return formatNumber(value);
    }
    
    return value;
  };

  const cellRenderer = renderCell || defaultRenderCell;

  return (
    <div className={`bg-white rounded-lg ${className}`}>
      {/* Table Container with horizontal scroll */}
      <div 
        className="w-full overflow-x-auto"
      >
        <Table className="min-w-[900px] text-xl table-fixed !border-none box-shadow-none"
               style={{ tableLayout: 'fixed' }}
        >
            <TableHeader>
              <TableRow className="border-none hover:bg-white !box-shadow-none">
                {columns.map((column, index) => (
                  <TableHead 
                    key={column.key}
                    className={`text-center py-4 px-1 xl:px-6 text-[#6F6F6E] font-medium w-[150px] ${column.className || ''}`}
                  >
                    <div className='w-full flex items-center justify-center'>
                      <div className={`flex items-center justify-center py-4 border-b border-gray-400 w-full ${index == 0 ? 'text-[#000]' : ''}`}>
                        <span className="whitespace-nowrap text-center leading-tight">
                          {column.label}
                        </span>
                      </div>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((item, index) => (
                <TableRow 
                  key={index} 
                  className={`${index % 2 === 1 ? '' : 'bg-white hover:bg-white'} text-[#6F6F6E] border-0`}
                >
                  {columns.map((column) => (
                    <TableCell 
                      key={column.key}
                      className="text-center py-4 px-2 sm:px-6"
                    >
                      <div className="break-words text-center sm:overflow-hidden sm:text-ellipsis">
                        {cellRenderer(column, item, index)}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
  );
};