@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: #dddddd #ffffff;
}

/* Works on Chrome/Edge/Safari */
*::-webkit-scrollbar {
  width: 15px;
}

*::-webkit-scrollbar-track {
  background: #ffffff;
}

*::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 20px;
  border: 3px solid #ffffff;
}

.btn01 {
  width: 300px;
  background-color: #fff;
  border-radius: 9999px;
}
@media screen and (max-width: 767px) {
  .btn01 {
    width: 275px;
  }
}

@media screen and (max-width: 767px) {
  .contactContent01 .downloadBtn01 a::before {
    width: 6px;
    height: 6px;
    right: 10px;
  }
}
.contactContent01 li {
  width: 280px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.contactContent01 .mailBtn01 a {
  align-items: center;
  background: #fff;
  border: 1px solid #727272;
  border-radius: 20px;
  display: flex;
  font-weight: 700;
  height: 40px;
  justify-content: center;
  position: relative;
  width: 100%;
}

.contactContent01 .mailBtn01 button {
  align-items: center;
  background: #fff;
  border: 1px solid #727272;
  border-radius: 20px;
  display: flex;
  font-weight: 700;
  height: 40px;
  justify-content: center;
  position: relative;
  width: 100%;
}

.contactContent01 .downloadBtn01 {
  background: transparent;
}

.contactContent01 .downloadBtn01 a,
.contactContent01 .downloadBtn01 button {
  font-size: 16px;
  width: 100%;
  height: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.btn01 a,
.btn01 button {
  border-radius: 25px !important;
  color: #fff;
  display: block;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1px;
  overflow: hidden;
  padding: 12px 15px 11px;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* ul > li a {
  color: #302ce1;
  padding: 2px 4px;
} */
/* .layout-contact_nav > ul > li > a:hover {
  text-decoration: underline;
} */

@media screen and (max-width: 767px) {
  .contactContent01 li {
    width: 55%;
  }
}

.contactContent01 .mailBtn01 {
  margin-right: 100px;
}

@media screen and (max-width: 1000px) {
  .contactContent01 .mailBtn01 {
    margin-right: 50px;
  }
}

@media screen and (max-width: 767px) {
  .contactContent01 .mailBtn01 {
    margin-right: 0;
  }
}

.contactContent01 .mailBtn01 a::before {
  content: "";
  width: 8px;
  height: 8px;
  margin: auto;
  border: 0px;
  border-top: solid 1px #727272;
  border-right: solid 1px #727272;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 15px;
  z-index: 1;
}

@media screen and (max-width: 767px) {
  .contactContent01 .mailBtn01 a::before {
    width: 6px;
    height: 6px;
    right: 10px;
  }
}

.contactContent01 .mailBtn01 img {
  margin-right: 5px;
}

@media screen and (max-width: 767px) {
  .contactContent01 .mailBtn01 img {
    width: 16px;
  }
}

.btn01 a,
.btn01 button {
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 15px 11px;
  border-radius: 25px;
  position: relative;
  display: block;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  text-align: center;
  letter-spacing: 1px;
  overflow: hidden;
  border: 0px;
}
@media all and (-ms-high-contrast: none) {
  .btn01 a,
  .btn01 button {
    padding: 15px 15px 8px;
  }
}
@media screen and (max-width: 767px) {
  .btn01 a,
  .btn01 button {
    padding: 6.5px 10px;
  }
}
.btn01 a:after,
.btn01 button:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}
.btn01 a:before,
.btn01 button:before {
  content: "";
  width: 8px;
  height: 8px;
  margin: auto;
  border: 0px;
  border-top: solid 1px #fff;
  border-right: solid 1px #fff;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 15px;
  z-index: 1;
}
.btn01 a span,
.btn01 button span {
  position: relative;
  z-index: 1;
}
.btn01 a:hover,
.btn01 button:hover {
  opacity: 1;
}
.btn01 a:hover:after,
.btn01 button:hover:after {
  opacity: 0;
}
/* Custom btn gradient */
.greenBtn01 {
  transition: all 0.3s ease;
  background: linear-gradient(
    90deg,
    #13ae9c 0%,
    #56d592 30%,
    #56d592 70%,
    #63f2bb 100%
  );
  background-size: 200% 100%;
  background-position: 0% 0%;
}

.greenBtn01:hover {
  background-position: 100% 0%;
}

.greenBtn01 a,
.greenBtn01 button {
  background: #56d592;
  /* Old browsers */
  /* FF3.6-15 */
  /* Chrome10-25,Safari5.1-6 */
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(30%, #56d592),
    to(#63f1ba)
  );
  background: linear-gradient(to right, #56d592 30%, #63f1ba 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#56d592", endColorstr="#63f2bb",GradientType=1 );
  /* IE6-9 */
}
.greenBtn01 a:after,
.greenBtn01 button:after {
  background: #13ae9c;
  /* FF3.6-15 */
  /* Chrome10-25,Safari5.1-6 */
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(30%, #13ae9c),
    to(#56d592)
  );
  background: linear-gradient(to right, #13ae9c 30%, #56d592 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#13ae9c", endColorstr="#56d592",GradientType=1 );
  /* IE6-9 */
}
/* Checkbox */
.checkbox input + span:before {
  background-color: #fff;
  border: 1px solid #505050;
  content: "";
  display: inline-block;
  height: 20px;
  margin-right: 5px;
  vertical-align: sub;
  width: 20px;
}
.checkbox input:checked + span:before {
  background-image: url(data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%20stroke%3D%22%2313AE9C%22%20stroke-width%3D%223%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%3Cpolyline%20points%3D%2220%206%209%2017%204%2012%22%3E%3C%2Fpolyline%3E%3C%2Fsvg%3E);
  background-position: 0 1px;
  background-repeat: no-repeat;
}
*,
::after,
::before {
  box-sizing: border-box;
}
.checkbox span {
  color: #505050;
  font-size: 16px;
  font-weight: 700;
  line-height: 0;
}

/* Hide number input spinners */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
